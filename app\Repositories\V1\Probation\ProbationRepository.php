<?php

namespace App\Repositories\V1\Probation;

use App\Models\ProbationRequest;
use App\Repositories\BaseRepository;
use App\Traits\QueriesHelper;

class ProbationRepository extends BaseRepository
{
    use QueriesHelper;

    public function model(): string
    {
        return ProbationRequest::class;
    }

    public function getPendingProbationRequestsCount($data)
    {
        $query = $this->model->where('status', 'pending')
            ->whereDate('probation_end_date', '>=', $data['payroll_from_date'])
            ->whereDate('probation_end_date', '<=', $data['payroll_to_date']);
        $this->appendScopeQuery($query);
        $count = $query->count();
        return $count;
    }

    public function getPendingProbationRequestsByEmployee(int $employeeId)
    {
        return $this->model->where('employee_id', $employeeId)
            ->where('status', 'pending')
            ->get();
    }

}