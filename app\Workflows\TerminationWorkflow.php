<?php

namespace App\Workflows;

use App\Models\Employee;
use App\Services\TerminationService;
use App\Traits\V1\NotificationRedirection;
use Illuminate\Support\Facades\Log;
use Workflow\ActivityStub;
use Workflow\SignalMethod;
use Workflow\WorkflowStub;

class TerminationWorkflow extends RequestWorkflow
{
    use NotificationRedirection;

    public $queue = 'termination_workflow';

    public function execute($data)
    {
        try {
            Log::info('Workflow is started termination');

            yield ActivityStub::make(AssignRequestActivity::class, $data);

            $request = $this->getRequest($data);

            $this->checkApprovalCycleStatus($request);
            yield WorkflowStub::await(fn () => ($this->is_completed));

            $this->getFinalStatus($data);

            $this->updateRequestStatus($data['request']);

            $this->updateEmployeeRequestStatus($data['employee_request']);

            $this->updateEmployeeStatus($data['employee_id'], $data['request']); // Update employee status instead of employee request status

        } catch (\Exception $e) {
            Log::info($e);
            // \Sentry\captureException($e);
        }
    }

    #[SignalMethod]
    public function setIsCompleted(bool $action)
    {
        $this->is_completed = $action;
    }

    public function getFinalStatus($data)
    {
        $requestObj = $data['request'];
        $requestObj?->refresh();
        if (isset($requestObj) && $requestObj->status != 'pending') {
            $this->finalStatus = $requestObj->status;

            Log::info('Final status force changed to : '.$this->finalStatus);

            return;
        }

        $countOfThen = count($this->requestWorkflowApprovals['then']);
        $countOfOR = count($this->requestWorkflowApprovals['or']);
        if ($countOfOR > 0 && ! in_array('pending', $this->requestWorkflowApprovals['or'])) {
            $this->finalStatus = $this->requestWorkflowApprovals['or'][0] == 'approved' ? 'approved' : 'rejected';
        } elseif ($countOfThen > 0
            && in_array('rejected', $this->requestWorkflowApprovals['then'])) {
            $this->finalStatus = 'rejected';
        } else {
            $this->finalStatus = 'approved';
        }

        Log::info('Final status is: '.$this->finalStatus);
    }

    public function isRequestCancelled($requestObj)
    {
        return isset($requestObj['status']) && $requestObj['status'] === 'cancelled';
    }

    public function updateRequestStatus($requestObj)
    {
        if ($this->finalStatus == 'approved') {
            $requestObj->update(['status' => 'approved']);
            $this->redirectNotificationsAfterRequestFinalized($requestObj, 'approved');
            Log::info('Request is approved');
        } else {
            $requestObj->update(['status' => 'rejected']);
            $this->redirectNotificationsAfterRequestFinalized($requestObj, 'rejected');
            Log::info('Request is rejected');
        }
    }

    public function updateEmployeeRequestStatus($employeeRequestObj)
    {
        if ($this->finalStatus == 'approved') {
            $employeeRequestObj->update(['status' => 'approved']);
            Log::info('Employee Request is approved');
        } else {
            $employeeRequestObj->update(['status' => 'rejected']);
            Log::info('Employee Request is rejected');
        }
    }

    public function updateEmployeeStatus($employeeId, $requestObj)
    {
        $employee = Employee::find($employeeId);

        if (! $employee) {
            Log::error('Employee not found with ID: '.$employeeId);

            return;
        }

        if ($this->finalStatus == 'approved') {
            if ($requestObj->terminate_date <= date('Y-m-d')) {
                $employee->status = 'terminated';
                $employee->save();
                Log::info('Employee status updated to terminated for employee ID: '.$employeeId);
            } else {
                $employee->status = 'termination_pending';
                $employee->save();
                Log::info('Employee status updated to terminated for employee ID: '.$employeeId);
            }

            // Reject any pending probation requests for this employee when termination is approved
            $terminationService = app(TerminationService::class);
            $terminationService->rejectPendingProbationRequests($employeeId);
            Log::info('Pending probation requests rejected for employee ID: '.$employeeId);
        } elseif ($this->finalStatus == 'rejected') {
            $employee->employeeInfo->termination_date = null;
            $employee->employeeInfo->save();
        } else {
            Log::info('Employee status not updated due to request not being approved');
        }
    }
}
