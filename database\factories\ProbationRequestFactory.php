<?php

namespace Database\Factories;

use App\Models\Company;
use App\Models\Employee;
use App\Models\ProbationRequest;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\ProbationRequest>
 */
class ProbationRequestFactory extends Factory
{
    protected $model = ProbationRequest::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'company_id' => Company::factory(),
            'employee_id' => function ($attributes) {
                return Employee::factory()->state(['company_id' => $attributes['company_id']]);
            },
            'status' => 'pending',
            'probation_end_date' => Carbon::now()->addDays(30)->format('Y-m-d'),
            'workflow_id' => null,
        ];
    }
}
