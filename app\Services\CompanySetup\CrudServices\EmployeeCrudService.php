<?php

namespace App\Services\CompanySetup\CrudServices;

use App\Exceptions\UnprocessableException;
use App\FeatureToggles\Unleash;
use App\Http\Resources\Employee\EmployeeCollection;
use App\Models\Employee;
use App\Models\EmployeeInfo;
use App\Repositories\EmployeeHireHistoryRepository;
use App\Repositories\EmployeeRepository;
use App\Repositories\NewEmployeeRepository;
use App\Repositories\PayrollRepositories\PayrollsRepository;
use App\Repositories\Repository;
use App\Repositories\TerminationRequestRepository;
use App\Repositories\V1\EmployeeRequestRepository;
use App\Services\CompanySetup\BusinessServices\UpdateBranchManagerService;
use App\Services\CompanySetup\EmployeesService;
use App\Services\CrudService;
use App\Services\LeaveManagement\CrudServices\EmployeeLeaveRequestCrudService;
use App\Services\LeaveManagement\FillEmployeeBalancesService;
use App\Services\PayrollSetup\SystemSettingsService;
use App\Services\TerminationService;
use App\Traits\CICOHelper;
use App\Traits\GenerateRandomString;
use App\Util\BasicInfoUtil;
use Illuminate\Support\Arr;

class EmployeeCrudService extends CrudService
{
    use CICOHelper, GenerateRandomString;

    private Repository $employeeRepository;

    private Repository $branchRepository;

    private Repository $employeeLeaveRequestRepository;

    private Repository $cicoRepository;

    private Repository $attendanceDeductionRepository;

    private Repository $attendanceOvertimeRepository;

    private Repository $employeeLeaveBalanceRepository;

    private EmployeeRequestRepository $employeeRequestRepository;

    private Repository $attendanceRepository;

    private Repository $timecardRepository;

    private NewEmployeeRepository $newEmployeeRepository;

    private Repository $entityTagRepository;

    private TerminationRequestRepository $terminationRequestRepository;

    private EmployeeHireHistoryRepository $employeeHireHistoryRepository;

    private PayrollsRepository $payrollsRepository;

    public function __construct(
        private UpdateBranchManagerService $updateBranchManagerService,
        private UserCrudService $userCrudService,
        private RegisterationValidationCrudService $registerationValidationCrudService,
        private FillEmployeeBalancesService $fillEmployeeBalancesService,
        private EmployeeLeaveRequestCrudService $employeeLeaveRequestCrudService,
        private SystemSettingsService $systemSettingsService,
        private EmployeesService $employeesService,
        private TerminationService $terminationService,
    ) {
        parent::__construct('Employee');
        $this->branchRepository = Repository::getRepository('Branch');
        $this->employeeLeaveRequestRepository = Repository::getRepository('EmployeeLeaveRequest');
        $this->cicoRepository = Repository::getRepository('Cico');
        $this->attendanceDeductionRepository = Repository::getRepository('AttendanceDeduction');
        $this->attendanceOvertimeRepository = Repository::getRepository('AttendanceOvertime');
        $this->employeeLeaveBalanceRepository = Repository::getRepository('EmployeeLeaveBalance');
        $this->timecardRepository = Repository::getRepository('Timecard');
        $this->newEmployeeRepository = new NewEmployeeRepository;
        $this->attendanceRepository = Repository::getRepository('Attendance');
        $this->entityTagRepository = Repository::getRepository('EntityTag');
        $this->employeeRequestRepository = new EmployeeRequestRepository;
        $this->terminationRequestRepository = new TerminationRequestRepository;
        $this->employeeHireHistoryRepository = new EmployeeHireHistoryRepository;
        $this->payrollsRepository = new PayrollsRepository;

    }

    public function create(array $request, \stdClass &$output): void
    {
        if (! isset($request['branch_ids'])) {
            $request['branch_ids'] = [];
        }
        $request['branch_ids'][] = $request['branch_id'];
        $request['branch_ids'] = array_unique($request['branch_ids']);

        if (! $this->isValidCreate($request, $output)) {
            return;
        }
        $related_objects = $request['related_objects'] ?? [];

        $fourthName = isset($request['fourth_name']) ? ' '.$request['fourth_name'] : '';
        $fifthName = isset($request['fifth_name']) ? ' '.$request['fifth_name'] : '';
        $request['name'] = $request['first_name'].' '.$request['second_name'].' '.$request['third_name'].$fourthName.$fifthName;

        $branchIds = $request['branch_ids'];

        $employeeEntity = $this->repository->create($request, $related_objects);
        $request['employee_id'] = $employeeEntity->id;
        $employeeEntity->branches()->sync($branchIds);

        $userRequest = ['employee_id' => $employeeEntity->id, 'role_id' => $employeeEntity->title->role_id ?? null, 'name' => $employeeEntity->name];
        $this->userCrudService->create($userRequest, $output);

        $registerationValidationRequest = ['name' => $employeeEntity->name, 'phone' => $employeeEntity->phone, 'user_id' => $output->user->id];
        $this->registerationValidationCrudService->create($registerationValidationRequest, $output);

        if (isset($employeeEntity)) {
            $this->fillEmployeeBalancesService->fill([$employeeEntity]);
        }

        $roleId = $employeeEntity->title->role_id;
        $employeeEntity->user->assignRole([$roleId]);

        $output->{$this->entityName} = $employeeEntity;
        $this->employeeLeaveRequestCrudService->addProratedRequest(['employee_id' => $output->employee->id], $output);
    }

    public function liteCreate(array $request, \stdClass &$output): void
    {
        if (! isset($request['branch_ids'])) {
            $request['branch_ids'] = [];
        }
        $request['branch_ids'][] = $request['branch_id'];
        $request['branch_ids'] = array_unique($request['branch_ids']);

        if (! $this->isValidCreate($request, $output)) {
            return;
        }
        $branchRepository = Repository::getRepository('Branch');
        $request['employee_number'] = ('lite_add_'.(Employee::withoutGlobalScopes()->count() + 1));

        $this->create($request, $output);
    }

    public function update(array $request, \stdClass &$output): void
    {
        if (! $this->isValidUpdate($request, $output)) {
            return;
        }
        $related_objects = $request['related_objects'] ?? [];

        $fourthName = isset($request['fourth_name']) ? ' '.$request['fourth_name'] : '';
        $fifthName = isset($request['fifth_name']) ? ' '.$request['fifth_name'] : '';
        $request['name'] = $request['first_name'].' '.$request['second_name'].' '.$request['third_name'].$fourthName.$fifthName;

        $titleChanged = false;
        if (strcmp($this->entity->title_id, $request['title_id']) !== 0) {
            $titleChanged = true;
        }

        $newBranchIds = $request['branch_ids'] ?? [];

        if (! in_array($request['branch_id'], $newBranchIds)) {
            array_push($newBranchIds, $request['branch_id']);
        }
        if (isset($request['branch_ids'])) {
            unset($request['branch_ids']);
        }

        $updated_entity = $this->repository->update($this->entity, $request, $related_objects);
        $updated_entity->branches()->sync($newBranchIds); // make relations between the employee and the branches he belongs to
        if ($titleChanged) {
            $request['employee_id'] = $request['id'];
            $this->updateBranchManagerService->perform($request, $output);
        }
        $output->{$this->entityName} = $updated_entity;
    }

    public function isValidCreate(array $request, \stdClass &$output): bool
    {
        if (! parent::isValidCreate($request, $output)) {
            return false;
        }
        // $request['employee_number'] = $this->getEmployeeNumber($request, $output);
        // if(isset($output->Error)) return false;

        // employee number must be unique
        if (isset($request['employee_number'])) {
            $employeeNumberExists = $this->repository->getByKey('employee_number', $request['employee_number'])->exists();
            if ($employeeNumberExists) {
                $output->Error = ['Employee_number already exists', 'كود الموظف موجود بالفعل'];

                return false;
            }
        }

        // title id must be correct
        if (isset($request['title_id'])) {
            $titleRepository = Repository::getRepository('Title');
            $titleNotExists = is_null($titleRepository->getById($request['title_id']));
            if ($titleNotExists) {
                $output->Error = ['Invalid title id', 'المعرف الخاص بالوظيفة غير صحيح'];

                return false;
            }
        }

        // branch ids must be correct
        $branchRepository = Repository::getRepository('Branch');
        foreach ($request['branch_ids'] as $branch_id) {
            $branchNotExists = is_null($branchRepository->getById($branch_id));
            if ($branchNotExists) {
                $output->Error = ['Invalid branch id : '.$branch_id, ' رقم هذا الفرع غير موجود : '.$branch_id];

                return false;
            }
        }

        // phone number is unique
        // $phoneExists = !(is_null($this->repository
        // ->getByKey('phone', $request['phone'])->first()));
        // if ($phoneExists) {
        //     $output->Error = [' This phone number is already registered with another employee ', ' رقم الهاتف مسجل بالفعل لموظف أخر '];
        //     return false;
        // }

        // national ID is unique
        // $nationalIdExist = !(is_null($this->repository
        // ->getByKey('national_id', $request['national_id'])->first()));
        // if ($nationalIdExist) {
        //     $output->Error = [' This national ID is already registered with another employee ', ' الرقم القومى مسجل بالفعل لموظف أخر '];
        //     return false;
        // }

        return true;
    }

    public function isValidUpdate(array $request, \stdClass &$output): bool
    {
        if (! parent::isValidUpdate($request, $output)) {
            return false;
        }

        // $employeeCodeType = $this->systemSettingsService->getCompanyEmployeeCodeType() ?? null;
        // if(isset($request['employee_number']) && $employeeCodeType == BasicInfoUtil::EMPLOYEE_CODE_TYPES['AUTO'])
        //     return new UnprocessableException(trans('messages.can_not_edit_auto_employee_number'));
        // //employee number must be unique
        // if(isset($request['employee_number']) && $employeeCodeType == BasicInfoUtil::EMPLOYEE_CODE_TYPES['MANUAL']) {
        //     $employeeNumberExists = $this->repository
        //     ->getByKey('employee_number', $request['employee_number'])->exists();
        //     if ($employeeNumberExists && $request['employee_number'] != $this->entity->employee_number) {
        //         $output->Error = ['Employee_number already exists', 'كود الموظف موجود بالفعل'];
        //         return false;
        //     }
        // }

        // title id must be correct
        if (isset($request['title_id'])) {
            $titleRepository = Repository::getRepository('Title');
            $titleNotExists = is_null($titleRepository->getById($request['title_id']));
            if ($titleNotExists) {
                $output->Error = ['Invalid title id', 'المعرف الخاص الوظيفة غير صحيح'];

                return false;
            }
        }

        // branch ids must be correct
        if (isset($request['branch_ids'])) {
            $branchRepository = Repository::getRepository('Branch');
            foreach ($request['branch_ids'] as $branch_id) {
                $branchNotExists = is_null($branchRepository->getById($branch_id));
                if ($branchNotExists) {
                    $output->Error = ['Invalid branch id :', ' رقم هذا الفرع غير موجود :', $branch_id];

                    return false;
                }
            }
        }

        // phone number is unique
        $phoneExists = (isset($request['phone']) &&
            ! (is_null($this->repository->getByKey('phone', $request['phone'])->first())));
        if ($phoneExists && $request['phone'] != $this->entity->phone) {
            $output->Error = [' This phone number is already registered with another employee ', ' رقم الهاتف مسجل بالفعل لموظف أخر '];

            return false;
        }

        // national ID is unique
        $nationalIdExist = (isset($request['national_id'])
            && ! (is_null($this->repository->getByKey('national_id', $request['national_id'])->first())));
        if ($nationalIdExist && $request['national_id'] != $this->entity->national_id) {
            $output->Error = [' This national ID is already registered with another employee ', ' الرقم القومى مسجل بالفعل لموظف أخر '];

            return false;
        }

        return true;
    }

    public function delete(array $request, \stdClass &$output): void
    {

        foreach ($request['ids'] as $requestId) { // remove the relations with branches in branches_employees pivot table
            $employeeEntity = $this->repository->getById($requestId);
            if (isset($employeeEntity)) {
                $employeeEntity->branches()->detach();
            }
        }
        $this->repository->delete($request['ids']);
    }

    public function timecardsAndLeaves(array $data, \stdClass &$output): void
    {
        $unleash = app(Unleash::class);
        $data['is_new_work_types'] = $unleash->getNewWorkTypesFeatureFlag();

        $employees = $this->repository->timecardsAndLeaves($data);
        foreach ($employees as $employee) {
            //            if(isset($employee->employeeInfo->termination_date) && $employee->status != 'terminated'){
            //                $employee->status = 'termination_pending';
            //            }
            $employee['termination_date'] = $employee->employeeInfo->termination_date;
        }
        $output->employees = new EmployeeCollection($employees);
        $output->employeesCollection = $employees;
    }

    public function activate(array $request, \stdClass &$output): void
    {
        $employee = $this->repository->find($request['employee_id']);

        if (is_null($employee)) {
            $output->Error = ['Invalid employee id', 'المعرف الخاص بالموظف غير صحيح'];

            return;
        }

        $this->repository->setStatus($employee, config('globals.EMPLOYEE_STATUSES.ACTIVE'));
        $output->employee = $employee;
    }

    public function deactivate(array $request, \stdClass &$output): void
    {
        $employee = $this->repository->getById($request['employee_id']);

        if (is_null($employee)) {
            $output->Error = ['Invalid employee id', 'المعرف الخاص بالموظف غير صحيح'];

            return;
        }

        $this->repository->setStatus($employee, config('globals.EMPLOYEE_STATUSES.DEACTIVATED'));
        $output->employee = $employee;
    }

    // public function requestsPendingOnMeCount(array $request ,\stdClass &$output) :void
    // {
    //     $pendingOvertimes = $this->attendanceOvertimeRepository->pendingRequestsOnEmplyoee(auth()->user()->employee_id);
    //     $pendingDeductions = $this->attendanceDeductionRepository->pendingRequestsOnEmplyoee(auth()->user()->employee_id);
    //     $pendingLeaves = $this->employeeLeaveRequestRepository->pendingRequestsOnEmplyoee(auth()->user()->employee_id);
    //     $pendingUnverifiedCico = $this->cicoRepository->pendingUnverifiedRequestsOnEmplyoee(auth()->user()->employee_id);

    //     $output = $pendingOvertimes + $pendingDeductions + $pendingLeaves + $pendingUnverifiedCico;
    // }
    // public function requestsPendingOnMe(array $request, \stdClass &$output): void
    // {
    //     $branchIds = auth()->user()->employee->branches->pluck('id')->toArray();

    //     $pendingOnMeByBranch = $this->branchRepository->requestsPendingOnEmployee(auth()->user()->employee_id, $request['filter'], $branchIds);
    //     if (sizeof($pendingOnMeByBranch) && $pendingOnMeByBranch[0]->relationLoaded('employeeLeaveRequests')) {
    //         $pendingOnMeByBranch = $pendingOnMeByBranch->map(function ($branch) {

    //             $cicosToShow = [];
    //             foreach($branch->cicos as $cico){
    //                 if(isset($cico->pairedClock) && $this->validateManagerBranches($cico->pairedClock))
    //                     $cicosToShow[] = $cico;
    //             }

    //             unset($branch->cicos);
    //             $branch->cicos = $cicosToShow;

    //             foreach ($branch->employeeLeaveRequests as &$leaveRequest) {
    //                 $leaveRequest->remaining_balance = $this->employeeLeaveBalanceRepository->getBalanceOfLeave($leaveRequest)->balance ?? null;
    //                 $leaveRequest->unit = $leaveRequest->companyLeaveTypePolicy->unit ?? null;
    //                 unset($leaveRequest->companyLeaveTypePolicy);
    //             }
    //             return $branch;
    //         });
    //         // remove every branch that has no pending requests
    //         $pendingOnMeByBranch = $pendingOnMeByBranch->filter(function ($branch) {
    //             return sizeof($branch->cicos) > 0 || sizeof($branch->employeeLeaveRequests) > 0
    //                         || sizeof($branch->attendanceDeductions) > 0 || sizeof($branch->attendanceOvertimes) > 0;
    //         });

    //     }

    //     $output = array_values($pendingOnMeByBranch->toArray());
    // }

    public function addEmployeesInfo()
    {
        $employeeRepository = new EmployeeRepository('Employee');
        $employees = $employeeRepository->getAll()->get();

        foreach ($employees as $employee) {
            $employeeInfo = new EmployeeInfo;
            $employeeInfo->join_date = '2022-01-01';
            $employee->employeeInfo()->save($employeeInfo);
        }
    }

    public function terminateEmployee(array $data): void
    {

        $employeeId = $data['employee_id'];

        $terminateDate = $data['terminate_date'];

        $terminate_reason = $data['terminate_reason'] ?? '';

        $employee = $this->empolyeeExist($data['employee_id']);

        $this->isValidTerminate($employeeId, $terminateDate);

        $employee->employeeInfo->termination_date = $terminateDate;
        $employee->employeeInfo->termination_reason = $terminate_reason;

        $this->timecardRepository->deleteEmployeeTimecardsWithoutAttendanceFromDate($employee->id, $terminateDate);

        $employee->employeeInfo->save();

        $employee->revoke_date = $data['revoke_date'];
        $employee->status = $terminateDate > date('Y-m-d') ? 'termination_pending' : 'terminated';
        $employee->save();

        // Reject any pending probation requests for this employee
        $this->terminationService->rejectPendingProbationRequests($employeeId);
    }

    public function isValidTerminate(int $employeeId, string $terminateDate): void
    {
        $timecardsWithAttendanceCount = $this->timecardRepository->countEmployeeTimecardsWithAttendanceFromDate($employeeId, $terminateDate);

        if ($timecardsWithAttendanceCount > 0) {
            throw new UnprocessableException(trans('messages.terminated_employee_has_attendance'));
        }

    }

    public function empolyeeExist(int $employeeId)
    {
        $employee = $this->repository->find($employeeId);

        if (! isset($employee)) {
            throw new UnprocessableException(trans('messages.employee_not_found'));
        }

        if (! isset($employee->employeeInfo)) {
            throw new UnprocessableException(trans('messages.employee_info_not_found'));
        }

        return $employee;

    }

    public function cancelTermination(array $data): void
    {
        $employee = $this->empolyeeExist($data['employee_id']);

        $employee->employeeInfo->termination_date = null;
        $employee->employeeInfo->save();

        $employee->revoke_date = null;
        $employee->status = 'active';
        $employee->save();

    }

    public function getEmployeeNumber($request, \stdClass &$output)
    {
        $employeeCodeType = $this->systemSettingsService->getCompanyEmployeeCodeType() ?? null;

        $lastEmployeeNumber = $this->newEmployeeRepository->getLastEmployeeNumber() ?? '00000';

        if ($employeeCodeType == BasicInfoUtil::EMPLOYEE_CODE_TYPES['MANUAL'] && is_null(Arr::get($request, 'employee_number', null))) {
            $output->Error = ['Employee number is required', 'كود الموظف مطلوب'];

            return;
        }

        if (is_null($lastEmployeeNumber) && $employeeCodeType == BasicInfoUtil::EMPLOYEE_CODE_TYPES['AUTO']) {
            return '00001';
        }

        if ($employeeCodeType == BasicInfoUtil::EMPLOYEE_CODE_TYPES['AUTO']) {
            return $this->produceNewEmployeeNumber($lastEmployeeNumber);
        }

        return $request['employee_number'];
    }

    public function produceNewEmployeeNumber($oldNumber)
    {
        $lastChar = substr($oldNumber, -1);
        $subOldNumber = substr($oldNumber, 0, -1);
        $number = (int) $lastChar;
        $incrementedNumber = $number + 1;

        return $subOldNumber.$incrementedNumber;
    }

    private function getPublicHolidaysFlag()
    {
        $country = auth()->user()->company->country->name_en;
        if ($country == 'Egypt') {
            return true;
        }

        return false;
    }
}
