<?php

namespace Tests\Feature\V1\Running\TerminationFlow;

use App\Models\Employee;
use App\Models\EmployeeRequest;
use App\Models\Permission;
use App\Models\ProbationRequest;
use App\Models\Role;
use App\Models\User;
use App\Services\TerminationService;
use App\Traits\RefreshOnlyBluworksDatabase;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Tests\TestCase;

class TerminationProbationTest extends TestCase
{
    use RefreshOnlyBluworksDatabase;

    private User $user;
    private Role $role;
    private Employee $employee;
    private TerminationService $terminationService;

    protected function setUp(): void
    {
        parent::setUp();
        $this->prepareData();
    }

    private function prepareData(): void
    {
        $this->user = User::factory()->create();
        $this->role = Role::factory()->state(['name' => 'Super Admin', 'company_id' => $this->user->company_id])->create();
        $this->user->assignRole($this->role);

        // Create permissions for termination
        $permission = Permission::factory()->state(['name' => 'terminate_employee', 'guard_name' => 'user-api'])->create();
        $this->role->givePermissionTo($permission);

        // Create an employee
        $this->employee = Employee::factory()->create(['company_id' => $this->user->company_id]);

        $this->terminationService = app(TerminationService::class);

        // Set the authenticated user
        config(['globals.user' => $this->user]);
        config(['globals.company' => $this->user->company]);
    }

    public function testDirectTerminationRejectsPendingProbationRequests(): void
    {
        // Create a pending probation request for the employee
        $probationRequest = ProbationRequest::factory()->create([
            'employee_id' => $this->employee->id,
            'company_id' => $this->employee->company_id,
            'status' => 'pending',
            'probation_end_date' => Carbon::now()->addDays(30),
        ]);

        // Verify the probation request is pending
        $this->assertEquals('pending', $probationRequest->status);

        // Verify the employee request was created (this happens in the model boot method)
        $employeeRequest = EmployeeRequest::where('requestable_id', $probationRequest->id)
            ->where('requestable_type', 'probation_request')
            ->first();
        $this->assertNotNull($employeeRequest);
        $this->assertEquals('pending', $employeeRequest->status);

        // Perform direct termination
        $terminationData = [
            'employee_id' => $this->employee->id,
            'terminate_date' => Carbon::now()->addDays(1)->format('Y-m-d'),
            'terminate_reason' => 'performance',
            'terminate_type' => 'Termination',
        ];

        $this->terminationService->directTerminate($terminationData);

        // Verify the probation request is now rejected
        $probationRequest->refresh();
        $this->assertEquals('rejected', $probationRequest->status);

        // Verify the employee request is also rejected
        $employeeRequest->refresh();
        $this->assertEquals('rejected', $employeeRequest->status);
    }

    public function testTerminationApprovalRejectsPendingProbationRequests(): void
    {
        // Create a pending probation request for the employee
        $probationRequest = ProbationRequest::factory()->create([
            'employee_id' => $this->employee->id,
            'company_id' => $this->employee->company_id,
            'status' => 'pending',
            'probation_end_date' => Carbon::now()->addDays(30),
        ]);

        // Create a termination request
        $terminationRequest = \App\Models\TerminationRequest::factory()->create([
            'employee_id' => $this->employee->id,
            'terminate_date' => Carbon::now()->addDays(1)->format('Y-m-d'),
            'terminate_reason' => 'performance',
            'terminate_type' => 'Termination',
            'status' => 'pending',
        ]);

        // Create the corresponding employee request
        $terminationEmployeeRequest = EmployeeRequest::create([
            'company_id' => $this->employee->company_id,
            'requested_by' => $this->user->employee_id,
            'requestable_id' => $terminationRequest->id,
            'requestable_type' => 'termination_request',
            'request_name' => 'termination_request',
            'status' => 'pending',
            'employee_id' => $this->employee->id,
            'date' => now(),
        ]);

        // Verify initial states
        $this->assertEquals('pending', $probationRequest->status);
        $this->assertEquals('pending', $terminationRequest->status);

        // Mock the workflow completion check to return true
        $this->terminationService = $this->getMockBuilder(TerminationService::class)
            ->onlyMethods(['checkRequestIsCompleted', 'getFinalStatus', 'doAnAction'])
            ->setConstructorArgs([
                app(\App\Services\CompanySetup\BusinessServices\UpdateBranchManagerService::class),
                app(\App\Services\CompanySetup\CrudServices\UserCrudService::class),
                app(\App\Services\CompanySetup\CrudServices\RegisterationValidationCrudService::class),
                app(\App\Services\LeaveManagement\FillEmployeeBalancesService::class),
                app(\App\Services\LeaveManagement\CrudServices\EmployeeLeaveRequestCrudService::class),
                app(\App\Services\PayrollSetup\SystemSettingsService::class),
                app(\App\Services\CompanySetup\EmployeesService::class),
                app(\App\Services\V1\LeaveManagement\FillEmployeeBalancesService::class),
                app(\App\Repositories\V1\Holidays\PublicHolidayAbsenceRepository::class),
                app(\App\Services\PayrollSetup\EmployeeSalariesService::class),
                app(\App\Services\PayrollSetup\EmployeeInsurancesService::class),
                app(\App\Repositories\V1\Probation\ProbationRepository::class),
            ])
            ->getMock();

        $this->terminationService->method('checkRequestIsCompleted')->willReturn(true);
        $this->terminationService->method('getFinalStatus')->willReturn('approved');
        $this->terminationService->method('doAnAction')->willReturn(true);

        // Perform termination approval action
        $this->terminationService->actionOnTermination($terminationRequest, 'approve');

        // Verify the probation request is now rejected
        $probationRequest->refresh();
        $this->assertEquals('rejected', $probationRequest->status);

        // Verify the employee request is also rejected
        $probationEmployeeRequest = $probationRequest->employeeRequest;
        $this->assertEquals('rejected', $probationEmployeeRequest->status);
    }

    public function testMultiplePendingProbationRequestsAreAllRejected(): void
    {
        // Create multiple pending probation requests for the employee
        $probationRequest1 = ProbationRequest::factory()->create([
            'employee_id' => $this->employee->id,
            'company_id' => $this->employee->company_id,
            'status' => 'pending',
            'probation_end_date' => Carbon::now()->addDays(30),
        ]);

        $probationRequest2 = ProbationRequest::factory()->create([
            'employee_id' => $this->employee->id,
            'company_id' => $this->employee->company_id,
            'status' => 'pending',
            'probation_end_date' => Carbon::now()->addDays(60),
        ]);

        // Perform direct termination
        $terminationData = [
            'employee_id' => $this->employee->id,
            'terminate_date' => Carbon::now()->addDays(1)->format('Y-m-d'),
            'terminate_reason' => 'performance',
            'terminate_type' => 'Termination',
        ];

        $this->terminationService->directTerminate($terminationData);

        // Verify both probation requests are now rejected
        $probationRequest1->refresh();
        $probationRequest2->refresh();
        $this->assertEquals('rejected', $probationRequest1->status);
        $this->assertEquals('rejected', $probationRequest2->status);

        // Verify their employee requests are also rejected
        $employeeRequest1 = $probationRequest1->employeeRequest;
        $employeeRequest2 = $probationRequest2->employeeRequest;
        $this->assertEquals('rejected', $employeeRequest1->status);
        $this->assertEquals('rejected', $employeeRequest2->status);
    }

    public function testNonPendingProbationRequestsAreNotAffected(): void
    {
        // Create probation requests with different statuses
        $approvedProbationRequest = ProbationRequest::factory()->create([
            'employee_id' => $this->employee->id,
            'company_id' => $this->employee->company_id,
            'status' => 'approved',
            'probation_end_date' => Carbon::now()->addDays(30),
        ]);

        $rejectedProbationRequest = ProbationRequest::factory()->create([
            'employee_id' => $this->employee->id,
            'company_id' => $this->employee->company_id,
            'status' => 'rejected',
            'probation_end_date' => Carbon::now()->addDays(60),
        ]);

        // Perform direct termination
        $terminationData = [
            'employee_id' => $this->employee->id,
            'terminate_date' => Carbon::now()->addDays(1)->format('Y-m-d'),
            'terminate_reason' => 'performance',
            'terminate_type' => 'Termination',
        ];

        $this->terminationService->directTerminate($terminationData);

        // Verify non-pending probation requests are not affected
        $approvedProbationRequest->refresh();
        $rejectedProbationRequest->refresh();
        $this->assertEquals('approved', $approvedProbationRequest->status);
        $this->assertEquals('rejected', $rejectedProbationRequest->status);
    }
}
